import { DotsHorizontalIcon } from '@radix-ui/react-icons'
import { Row } from '@tanstack/react-table'
import { IconEdit, IconTrash, IconEye } from '@tabler/icons-react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Member } from '../data/schema'
import { useMembers } from '../context/members-context'
import { useNavigate } from '@tanstack/react-router'
import { END_POINTS } from '../utils/constant'
import { toTitleCase } from '../utils/utilities'
import { useQueryClient } from '@tanstack/react-query'
import { memberStatusChange } from '../api'

interface DataTableRowActionsProps {
  row: Row<Member>
}

export function DataTableRowActions({ row }: DataTableRowActionsProps) {
  const { setOpen, setCurrentRow } = useMembers()
  const navigate = useNavigate()
  const queryClient = useQueryClient();
  const { mutateAsync: statusChangeMutation } = memberStatusChange()

  const handleAction = async (action: any) => {
    if (action === 'view') {
      navigate({
        to: END_POINTS.MEMBERS_PROFILE + '/$memberId',
        params: { memberId: row.original.id }
      })
      return
    } else if (action === 'edit') {
      navigate({
        to: `${END_POINTS.UPDATE_MEMBER}/${row.original.id}`,
      })
      return
    } else if (action === "deactivate") {
      const response: any = await statusChangeMutation(row.original.id);
      if (response?.success) {
        queryClient.invalidateQueries({ queryKey: ['members-list'] });
      }
    }

    setCurrentRow(row.original)
    setOpen(action)
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant='ghost'
          className='data-[state=open]:bg-muted flex h-8 w-8 p-0 cursor-pointer'
        >
          <DotsHorizontalIcon className='h-4 w-4' />
          <span className='sr-only'>Open menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end' className='w-[160px]'>
        <DropdownMenuItem onClick={() => handleAction('view')}>
          <IconEye className='mr-2 h-3.5 w-3.5 text-muted-foreground/70' />
          View Profile
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleAction('deactivate')}>
          <IconEdit className='mr-2 h-3.5 w-3.5 text-muted-foreground/70' />
          {row?.original?.isSuspended ? "Active" : "Inactive"}
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => handleAction('delete')}>
          <IconTrash className='mr-2 h-3.5 w-3.5 text-muted-foreground/70' />
          Delete
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
