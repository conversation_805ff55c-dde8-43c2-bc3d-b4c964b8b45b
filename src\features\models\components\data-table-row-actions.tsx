import { DotsHorizontalIcon } from '@radix-ui/react-icons'
import { Row } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Model } from '../data/models'
import { useModels } from '../context/models-context'
import { useNavigate } from '@tanstack/react-router'
import { deleteModelApi, modelStatusChange } from '../api'
import { useQueryClient } from '@tanstack/react-query'
import { useState } from 'react'
import { ConfirmDialog } from '@/components/confirm-dialog'
import { toTitleCase } from '@/features/members/utils/utilities'
import { END_POINTS } from '@/features/members/utils/constant'

interface DataTableRowActionsProps {
  readonly row: Row<Model>
}

export function DataTableRowActions({ row }: any) {
  const { setOpen, setCurrentRow } = useModels()
  const navigate = useNavigate()
  const { mutateAsync: deleteModelMutation } = deleteModelApi()
  const { mutateAsync: updateModelMutation } = modelStatusChange()
  const queryClient = useQueryClient();
  const [show, setShow] = useState(false)

  const handleAction = async (action: any) => {
    switch (action) {
      case 'view':
        navigate({
          to: `${END_POINTS.MODEL_PROFILE}/$modelId`,
          params: { modelId: row.original.id },
        });
        break;

      case 'edit':
        navigate({
          to: `${END_POINTS.UPDATE_MODEL}/${row.original.id}`,
        });
        break;

      case 'deactivate':
        const response: any = await updateModelMutation(row.original.id)
        if (response?.success) {
          queryClient.invalidateQueries(['models-list']);
        }
        break;

      case 'delete':
        setShow(true)

        break;

      default:
        break;
    }

    setCurrentRow(row.original);
    setOpen(action);
  };

  const handleDelete = async () => {
    // const res: any = await deleteModelMutation(row.original);
    // if (res?.success) {
    setShow(false)
    //   queryClient.invalidateQueries(['models-list']);
    // }
  }


  return (
    <DropdownMenu modal={false}>
      <DropdownMenuTrigger asChild>
        <Button
          variant='ghost'
          className='data-[state=open]:bg-muted flex h-8 w-8 p-0 cursor-pointer'
        >
          <DotsHorizontalIcon className='h-4 w-4' />
          <span className='sr-only'>Open menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end' className='w-[200px]'>
        <DropdownMenuItem onClick={() => handleAction('view')}>
          View Profile
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleAction('edit')}>
          Edit Profile
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleAction('pictures')}>
          Pictures
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleAction('deactivate')}>
          {row?.original?.isSuspended ? "Active" : "Inactive"}
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleAction('delete')}>
          Delete
        </DropdownMenuItem>
      </DropdownMenuContent>

      <ConfirmDialog
        open={show}
        onOpenChange={() => { setShow(false) }}
        title="Are you sure?"
        desc="This action cannot be undone. This will permanently delete the model."
        confirmText="Delete"
        cancelBtnText="Cancel"
        destructive
        isLoading={false}
        handleConfirm={handleDelete}
      />
    </DropdownMenu>
  )
}
